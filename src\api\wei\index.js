import request from '@/router/axios';


// 设备总数 检测数量 阳性数量 已处置量
export const getCount1 = (params) => {
  return request({
    url: '/api/index/count1',
    method: 'get',
    params: {
      ...params
    }
  })
}
//查询对应部门
export const getDept = () => {
  return request({
    url: '/api/index/dept',
    method: 'get',
  })
}
//查询消息列表
export const getNotice = () => {
  return request({
    url: '/api/blade-desk/notice/list?current=1&size=10',
    method: 'get',
  })
}
//查询随机三个视频
export const getVideo = () => {
  return request({
    url: '/api/index/video',
    method: 'get',
  })
}

//各检测室检测量排行 总量
export const getCount2 = (params) => {
  return request({
    url: '/api/index/count2',
    method: 'get',
    params: {
      ...params
    }
  })
}
//各检测室检测量排行 阳性
export const getCount3 = (params) => {
  return request({
    url: '/api/index/count3',
    method: 'get',
    params: {
      ...params
    }
  })
}
//阳性预警
export const getCount7 = (params) => {
  return request({
    url: '/api/index/count7',
    method: 'get',
    params: {
      ...params
    }
  })
}
///不同类型占比-总量
export const getCount8 = (params) => {
  return request({
    url: '/api/index/count8',
    method: 'get',
    params: {
      ...params
    }
  })
}
//不同类型占比-阳性
export const getCount9 = (params) => {
  return request({
    url: '/api/index/count9',
    method: 'get',
    params: {
      ...params
    }
  })
}
//三级任务AI绩效统计
export const getCount10 = (params) => {
  return request({
    url: '/api/index/count10',
    method: 'get',
    params: {
      ...params
    }
  })
}
























// 检测-食品质量安全 /tb/getSYgl

export const getSYgl = (params) => {
  return request({
    url: '/api/tb/getSYgl',
    method: 'get',
    params: {
      ...params
    }
  })
}

export const getSYlbYxl = (params) => {
    return request({
      url: '/api/tb/getSYlbYxl',
      method: 'get',
      params: {
        ...params
      }
    })
  }

// /tb/getSYlbJcl 类别检测量

export const getSYlbJcl = (params) => {
  return request({
    url: '/api/tb/getSYlbJcl',
    method: 'get',
    params: {
      ...params
    }
  })
}

// /tb/getSySph 首页-检测市场排名

export const getSySph = (params) => {
  return request({
    url: '/api/tb/getSySph',
    method: 'get',
    params: {
      ...params
    }
  })
}

// /tb/getSYyxYj 首页-阳性报警
export const getSYyxYj = (params) => {
  return request({
    url: '/api/tb/getSYyxYj',
    method: 'get',
    params: {
      ...params
    }
  })
}
// /tb/getSYsbXz

export const getSYsbXz = (params) => {
  return request({
    url: '/api/tb/getSYsbXz',
    method: 'get',
    params: {
      ...params
    }
  })
}

// /tb/getSYjcSjb


export const getSYjcSjb = (params) => {
  return request({
    url: '/api/tb/getSYjcSjb',
    method: 'get',
    params: {
      ...params
    }
  })
}


// /tb/getSYjcJlb
export const getSYjcJlb = (params) => {
  return request({
    url: '/api/tb/getSYjcJlb',
    method: 'get',
    params: {
      ...params
    }
  })
}
